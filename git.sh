#!/bin/bash

# 🏠 综合项目推送脚本
# 功能：将包含所有子项目的综合项目推送到私有库
# 版本：v2.0
# 特性：一键推送、大文件支持、简单易用

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_DIR="$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 子项目列表（用于显示）
SUBPROJECTS=("gpt-load" "love" "new-api")

# Git Bundle 配置
BUNDLE_DIR="git-bundles"
RESTORE_SCRIPT="restore-git-repos.sh"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 等待用户按键 - 默认Enter继续
wait_for_key() {
    echo
    echo -e "${CYAN}按 Enter 键继续...${NC}"
    read -r
}

# 确认操作 - 默认为 Y
confirm_action() {
    local message="$1"
    local response

    echo -n -e "${YELLOW}$message [Y/n]: ${NC}"
    read -r response

    # 如果用户直接按回车或输入Y，返回成功
    case "$response" in
        ""|[Yy]|[Yy][Ee][Ss])
            return 0
            ;;
        [Nn]|[Nn][Oo])
            return 1
            ;;
        *)
            # 无效输入，默认为Y
            return 0
            ;;
    esac
}

# 检查是否为 Git 仓库
check_git_repo() {
    local dir="${1:-$PWD}"
    [ -d "$dir/.git" ] && git -C "$dir" rev-parse --git-dir >/dev/null 2>&1
}

# 获取文件大小（字节）
get_file_size() {
    local file="$1"
    if [ -f "$file" ]; then
        stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "0"
    else
        echo "0"
    fi
}

# 检查是否为大文件（超过100MB）
is_large_file() {
    local file="$1"
    local max_size=$((100 * 1024 * 1024))  # 100MB
    local file_size=$(get_file_size "$file")
    [ "$file_size" -gt "$max_size" ]
}

# 检查子项目是否有Git仓库
has_git_repo() {
    local project="$1"
    [ -d "$project/.git" ] && git -C "$project" rev-parse --git-dir >/dev/null 2>&1
}

# 获取子项目Git信息
get_git_info() {
    local project="$1"
    if has_git_repo "$project"; then
        local branch=$(git -C "$project" branch --show-current 2>/dev/null || echo "detached")
        local commit=$(git -C "$project" rev-parse --short HEAD 2>/dev/null || echo "no-commit")
        local remote=$(git -C "$project" remote get-url origin 2>/dev/null || echo "no-remote")
        echo "分支: $branch | 提交: $commit"
    else
        echo "无Git仓库"
    fi
}

# 创建Git Bundle目录
create_bundle_dir() {
    if [ ! -d "$BUNDLE_DIR" ]; then
        mkdir -p "$BUNDLE_DIR"
        log_success "创建Bundle目录: $BUNDLE_DIR"
    fi
}

# 查看项目状态
view_project_status() {
    echo -e "${CYAN}=== 📊 查看项目状态 ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    # 检查主项目状态
    echo "========================================"
    echo "综合项目状态"
    echo "========================================"

    if ! check_git_repo; then
        log_error "当前目录不是 Git 仓库"
        echo "请先运行: git init"
        return 1
    fi

    local current_branch=$(git branch --show-current 2>/dev/null || echo "未知")
    local current_commit=$(git rev-parse --short HEAD 2>/dev/null || echo "无提交")
    echo "当前分支: $current_branch"
    echo "当前提交: $current_commit"

    # 检查远程仓库
    echo "远程仓库:"
    git remote -v 2>/dev/null || echo "  无远程仓库"
    echo

    # 检查子项目状态
    echo "========================================"
    echo "包含的子项目"
    echo "========================================"

    for project in "${SUBPROJECTS[@]}"; do
        if [ -d "$project" ]; then
            local file_count=$(find "$project" -type f -not -path "*/.git/*" | wc -l)
            log_success "$project: $file_count 个文件"

            # 显示子项目的独立Git状态（如果有）
            if has_git_repo "$project"; then
                local git_info=$(get_git_info "$project")
                echo "  ↳ 独立Git仓库: $git_info"
            fi
        else
            log_warning "$project: 目录不存在"
        fi
    done

    echo

    # 检查Git状态
    echo "========================================"
    echo "Git 状态"
    echo "========================================"

    local untracked=$(git ls-files --others --exclude-standard | wc -l)
    local modified=$(git diff --name-only | wc -l)
    local staged=$(git diff --cached --name-only | wc -l)

    echo "未跟踪文件: $untracked"
    echo "已修改文件: $modified"
    echo "已暂存文件: $staged"

    if [ $((untracked + modified + staged)) -gt 0 ]; then
        log_info "有文件需要提交"
    else
        log_success "工作目录干净"
    fi

    echo
    log_success "项目状态查看完成"
    return 0
}

# 添加所有文件到Git
add_all_files() {
    echo -e "${CYAN}=== 📁 添加文件到Git ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    log_info "添加所有文件到Git（排除子项目.git目录）..."

    # 首先移除可能存在的子模块引用
    for project in "${SUBPROJECTS[@]}"; do
        if [ -d "$project" ]; then
            # 如果子项目被识别为子模块，先移除子模块引用
            if git ls-files --stage | grep -q "^160000.*$project$"; then
                log_info "移除子项目 $project 的子模块引用..."
                git rm --cached "$project" 2>/dev/null || true
            fi
        fi
    done

    # 添加所有文件，.gitignore会自动排除子项目的.git目录
    git add .

    # 检查添加的文件数量
    local added_files=$(git diff --cached --name-only | wc -l)

    if [ "$added_files" -gt 0 ]; then
        log_success "已添加 $added_files 个文件"

        # 显示部分文件列表
        echo "部分文件列表："
        git diff --cached --name-only | head -10 | sed 's/^/  /'
        if [ "$added_files" -gt 10 ]; then
            echo "  ... 还有 $((added_files - 10)) 个文件"
        fi
    else
        log_info "没有新文件需要添加"
    fi

    return 0
}

# 提交更改
commit_changes() {
    echo -e "${CYAN}=== 💾 提交更改 ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    # 检查是否有更改需要提交
    if git diff --cached --quiet; then
        log_info "没有更改需要提交"
        return 0
    fi

    # 生成提交消息
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local added_files=$(git diff --cached --name-only | wc -l)

    local commit_msg="Workspace update - $timestamp

包含所有子项目的完整更新：
$(for project in "${SUBPROJECTS[@]}"; do
    if [ -d "$project" ]; then
        echo "- $project"
    fi
done)

更新文件数: $added_files
更新时间: $timestamp

这是一个综合项目的完整快照，包含所有子项目的源代码。
可以通过 git clone 一键获取所有内容。"

    log_info "提交更改..."
    if git commit -m "$commit_msg"; then
        log_success "提交成功"
        return 0
    else
        log_error "提交失败"
        return 1
    fi
}

# 推送到远程仓库
push_to_remote() {
    echo -e "${CYAN}=== 🚀 推送到远程仓库 ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    # 检查是否有远程仓库
    if ! git remote | grep -q origin; then
        log_error "没有配置远程仓库"
        echo "请先添加远程仓库："
        echo "  git remote add origin <your-private-repo-url>"
        return 1
    fi

    local current_branch=$(git branch --show-current)
    log_info "推送到远程仓库 origin/$current_branch ..."

    if git push origin "$current_branch" 2>&1; then
        log_success "推送成功！"
        echo
        echo "现在可以在其他机器上使用以下命令获取完整项目："
        echo "  git clone <your-private-repo-url>"
        return 0
    else
        log_error "推送失败"
        echo
        echo "可能的解决方案："
        echo "1. 检查网络连接"
        echo "2. 检查远程仓库权限"
        echo "3. 如果是首次推送，尝试："
        echo "   git push -u origin $current_branch"
        return 1
    fi
}

# 创建子项目Git Bundle
create_git_bundles() {
    echo -e "${CYAN}=== 📦 创建Git Bundle ===${NC}"
    echo

    cd "$WORKSPACE_DIR"
    create_bundle_dir

    local bundle_count=0
    local total_size=0

    for project in "${SUBPROJECTS[@]}"; do
        if has_git_repo "$project"; then
            log_info "为 $project 创建Git Bundle..."

            local bundle_file="$BUNDLE_DIR/${project}.bundle"

            # 进入子项目目录创建bundle
            if (cd "$project" && git bundle create "../$bundle_file" --all 2>/dev/null); then
                local bundle_size=$(get_file_size "$bundle_file")
                local size_mb=$((bundle_size / 1024 / 1024))

                log_success "$project Bundle创建完成 (${size_mb}MB)"
                bundle_count=$((bundle_count + 1))
                total_size=$((total_size + bundle_size))
            else
                log_warning "$project Bundle创建失败，可能没有提交历史"
                rm -f "$bundle_file"
            fi
        else
            log_info "$project 没有Git仓库，跳过Bundle创建"
        fi
    done

    if [ $bundle_count -gt 0 ]; then
        local total_mb=$((total_size / 1024 / 1024))
        log_success "创建了 $bundle_count 个Bundle文件，总大小: ${total_mb}MB"
        return 0
    else
        log_warning "没有创建任何Bundle文件"
        return 1
    fi
}

# 创建Git恢复脚本
create_restore_script() {
    echo -e "${CYAN}=== 📝 创建恢复脚本 ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    cat > "$RESTORE_SCRIPT" << 'EOF'
#!/bin/bash

# Git仓库自动恢复脚本
# 版本: v1.0
# 功能: 从Git Bundle恢复子项目的完整Git仓库

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 子项目列表
SUBPROJECTS=("gpt-load" "love" "new-api")
BUNDLE_DIR="git-bundles"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 确认操作
confirm_action() {
    local message="$1"
    echo -n -e "${YELLOW}$message [Y/n]: ${NC}"
    read -r response
    case "$response" in
        ""|[Yy]|[Yy][Ee][Ss]) return 0 ;;
        *) return 1 ;;
    esac
}

# 恢复单个项目的Git仓库
restore_project_git() {
    local project="$1"
    local bundle_file="$BUNDLE_DIR/${project}.bundle"

    if [ ! -f "$bundle_file" ]; then
        log_warning "$project: Bundle文件不存在，跳过"
        return 1
    fi

    if [ ! -d "$project" ]; then
        log_error "$project: 项目目录不存在"
        return 1
    fi

    if [ -d "$project/.git" ]; then
        log_warning "$project: 已存在Git仓库"
        if ! confirm_action "是否覆盖现有Git仓库？"; then
            return 1
        fi
        rm -rf "$project/.git"
    fi

    log_info "恢复 $project 的Git仓库..."

    # 创建临时目录
    local temp_dir=$(mktemp -d)

    # 从bundle克隆到临时目录
    if git clone "$bundle_file" "$temp_dir" 2>/dev/null; then
        # 移动.git目录到项目中
        mv "$temp_dir/.git" "$project/"

        # 进入项目目录重置工作区
        cd "$project"
        git reset --hard HEAD >/dev/null 2>&1

        # 显示恢复后的状态
        local branch=$(git branch --show-current 2>/dev/null || echo "detached")
        local commit=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

        log_success "$project Git仓库恢复完成 (分支: $branch, 提交: $commit)"
        cd ..

        # 清理临时目录
        rm -rf "$temp_dir"
        return 0
    else
        log_error "$project Git仓库恢复失败"
        rm -rf "$temp_dir"
        return 1
    fi
}

# 主函数
main() {
    echo -e "${CYAN}=== 🔄 Git仓库恢复工具 ===${NC}"
    echo

    if [ ! -d "$BUNDLE_DIR" ]; then
        log_error "Bundle目录不存在: $BUNDLE_DIR"
        echo "请确保在包含Git Bundle的目录中运行此脚本"
        exit 1
    fi

    # 统计可恢复的项目
    local available_bundles=0
    for project in "${SUBPROJECTS[@]}"; do
        if [ -f "$BUNDLE_DIR/${project}.bundle" ]; then
            available_bundles=$((available_bundles + 1))
        fi
    done

    if [ $available_bundles -eq 0 ]; then
        log_error "没有找到任何Bundle文件"
        exit 1
    fi

    log_info "找到 $available_bundles 个Bundle文件"
    echo

    if ! confirm_action "开始恢复Git仓库？"; then
        log_info "操作已取消"
        exit 0
    fi

    echo
    local success_count=0

    # 恢复每个项目
    for project in "${SUBPROJECTS[@]}"; do
        if restore_project_git "$project"; then
            success_count=$((success_count + 1))
        fi
    done

    echo
    if [ $success_count -gt 0 ]; then
        log_success "🎉 成功恢复 $success_count 个项目的Git仓库！"
        echo
        echo "现在您可以在各个子项目中正常使用Git命令："
        echo "  cd <project-name>"
        echo "  git status"
        echo "  git log"
        echo "  git push"
    else
        log_error "没有成功恢复任何Git仓库"
        exit 1
    fi
}

# 如果直接执行则调用主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
EOF

    chmod +x "$RESTORE_SCRIPT"
    log_success "恢复脚本创建完成: $RESTORE_SCRIPT"

    echo
    echo "使用方法："
    echo "  在其他机器上clone后，运行: ./$RESTORE_SCRIPT"
    echo "  即可恢复所有子项目的Git仓库功能"
}

# 一键推送（完整流程）
quick_push() {
    echo -e "${CYAN}=== ⚡ 一键推送综合项目 ===${NC}"
    echo

    if ! confirm_action "开始一键推送流程？"; then
        log_info "操作已取消"
        return 1
    fi

    # 执行完整流程
    if add_all_files && commit_changes && push_to_remote; then
        echo
        log_success "🎉 一键推送完成！"
        echo
        echo "综合项目已成功推送到私有库。"
        echo "在其他机器上可以通过 git clone 获取所有子项目的完整内容。"
        return 0
    else
        log_error "一键推送失败"
        return 1
    fi
}

# 一键推送（含Git历史）
quick_push_with_git() {
    echo -e "${CYAN}=== ⚡ 一键推送（含Git历史） ===${NC}"
    echo

    if ! confirm_action "开始一键推送流程（含Git历史）？"; then
        log_info "操作已取消"
        return 1
    fi

    # 创建Git Bundle和恢复脚本
    if create_git_bundles && create_restore_script; then
        echo
        log_info "Git Bundle创建完成，继续推送..."
    else
        log_warning "Git Bundle创建失败，继续普通推送..."
    fi

    # 执行完整流程
    if add_all_files && commit_changes && push_to_remote; then
        echo
        log_success "🎉 一键推送（含Git历史）完成！"
        echo
        echo "综合项目已成功推送到私有库。"
        echo "在其他机器上可以通过以下步骤获取完整功能："
        echo "1. git clone <your-private-repo-url>"
        echo "2. cd <cloned-directory>"
        echo "3. ./$RESTORE_SCRIPT"
        echo "4. 即可恢复所有子项目的Git仓库功能"
        return 0
    else
        log_error "一键推送失败"
        return 1
    fi
}

# 显示使用说明
show_help() {
    echo -e "${CYAN}=== 📚 使用说明 ===${NC}"
    echo
    echo "这是一个综合项目推送脚本，用于将包含多个子项目的工作空间"
    echo "推送到私有库，实现一键clone获取所有内容。"
    echo
    echo "========================================"
    echo "🎯 主要功能"
    echo "========================================"
    echo "• 查看项目状态和包含的子项目"
    echo "• 添加所有文件到Git（自动排除.git目录）"
    echo "• 提交更改并生成详细的提交信息"
    echo "• 推送到私有远程仓库"
    echo "• 一键完成整个流程"
    echo "• 🆕 创建Git Bundle保留完整Git历史"
    echo "• 🆕 生成自动恢复脚本"
    echo "• 🆕 支持在其他机器上恢复Git功能"
    echo
    echo "========================================"
    echo "🚀 使用流程"
    echo "========================================"
    echo "1. 首次使用："
    echo "   - 确保已配置远程仓库: git remote add origin <url>"
    echo "   - 选择 4 进行一键推送（含Git历史）"
    echo
    echo "2. 日常使用："
    echo "   - 普通备份：选择 3 进行一键推送"
    echo "   - 完整备份：选择 4 进行一键推送（含Git历史）"
    echo "   - 单独操作：选择 5 创建Bundle，选择 6 创建恢复脚本"
    echo
    echo "3. 其他机器获取："
    echo "   - git clone <your-private-repo-url>"
    echo "   - 如果需要Git功能：运行 ./restore-git-repos.sh"
    echo "   - 即可获得所有子项目的完整Git功能"
    echo
    echo "========================================"
    echo "⚠️ 注意事项"
    echo "========================================"
    echo "• 子项目的.git目录会被自动忽略"
    echo "• 确保.gitignore配置正确"
    echo "• 大文件建议使用Git LFS"
    echo "• 默认按Enter键表示确认"
    echo "• Git Bundle文件可能较大，请确保有足够空间"
    echo "• 恢复Git功能需要运行恢复脚本"
    echo
    wait_for_key
}

# 主菜单
main_menu() {
    while true; do
        clear
        echo -e "${CYAN}╔══════════════════════════════════════╗"
        echo -e "║     🏠 综合项目推送系统 v2.0        ║"
        echo -e "╚══════════════════════════════════════╝${NC}"
        echo

        # 显示当前状态
        if check_git_repo; then
            local branch=$(git branch --show-current 2>/dev/null || echo "未知")
            local remote=$(git remote | head -1 || echo "无")
            echo -e "${WHITE}当前状态:${NC} Git仓库 | 分支: $branch | 远程: $remote"
        else
            echo -e "${WHITE}当前状态:${NC} ${RED}未初始化Git仓库${NC}"
        fi

        # 显示包含的子项目
        echo -e "${WHITE}包含项目:${NC}"
        for project in "${SUBPROJECTS[@]}"; do
            if [ -d "$project" ]; then
                echo -e "  ${GREEN}✓${NC} $project"
            else
                echo -e "  ${RED}✗${NC} $project (不存在)"
            fi
        done
        echo

        echo -e "${WHITE}请选择操作:${NC}"
        echo -e "${GREEN}1)${NC} 📊 查看项目状态"
        echo -e "${GREEN}2)${NC} 📁 添加文件到Git"
        echo -e "${GREEN}3)${NC} ⚡ 一键推送（推荐）"
        echo -e "${GREEN}4)${NC} 🎯 一键推送（含Git历史）"
        echo -e "${GREEN}5)${NC} 📦 创建Git Bundle"
        echo -e "${GREEN}6)${NC} 🔄 创建恢复脚本"
        echo -e "${GREEN}7)${NC} 📚 使用说明"
        echo -e "${GREEN}0)${NC} 🚪 退出"
        echo
        echo -n -e "${CYAN}请输入选择 [0-7] 或直接按Enter退出: ${NC}"

        read -r choice

        # 如果直接按Enter，退出
        if [ -z "$choice" ]; then
            choice="0"
        fi

        case $choice in
            1)
                view_project_status
                wait_for_key
                ;;
            2)
                add_all_files
                wait_for_key
                ;;
            3)
                quick_push
                wait_for_key
                ;;
            4)
                quick_push_with_git
                wait_for_key
                ;;
            5)
                create_git_bundles
                wait_for_key
                ;;
            6)
                create_restore_script
                wait_for_key
                ;;
            7)
                show_help
                ;;
            0)
                echo -e "${CYAN}感谢使用综合项目推送系统！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选择，请重新输入${NC}"
                sleep 2
                ;;
        esac
    done
}

# 主函数
main() {
    cd "$WORKSPACE_DIR"

    # 如果没有参数，直接进入菜单
    if [ $# -eq 0 ]; then
        main_menu
        return
    fi

    # 处理命令行参数
    case "$1" in
        "status"|"s")
            view_project_status
            ;;
        "add"|"a")
            add_all_files
            ;;
        "commit"|"c")
            commit_changes
            ;;
        "push"|"p")
            push_to_remote
            ;;
        "quick"|"q")
            quick_push
            ;;
        "git"|"g")
            quick_push_with_git
            ;;
        "bundle"|"b")
            create_git_bundles
            ;;
        "restore"|"r")
            create_restore_script
            ;;
        "help"|"h")
            show_help
            ;;
        *)
            echo -e "${CYAN}=== 🏠 综合项目推送系统 v2.1 ===${NC}"
            echo
            echo "用法: $0 [命令]"
            echo
            echo "命令:"
            echo "  status, s    - 查看项目状态"
            echo "  add, a       - 添加文件到Git"
            echo "  commit, c    - 提交更改"
            echo "  push, p      - 推送到远程"
            echo "  quick, q     - 一键推送"
            echo "  git, g       - 一键推送（含Git历史）"
            echo "  bundle, b    - 创建Git Bundle"
            echo "  restore, r   - 创建恢复脚本"
            echo "  help, h      - 显示帮助"
            echo
            echo "不带参数运行将进入交互式菜单"
            ;;
    esac
}

# 如果直接执行则调用主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

# 清理历史跟踪记录
clean_history_tracking() {
    echo -e "${CYAN}=== 🧹 清理历史跟踪记录 ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    echo "此操作将："
    echo "1. 从Git历史中移除所有子项目文件的跟踪记录"
    echo "2. 保留子项目的当前文件内容"
    echo "3. 创建全新的干净历史"
    echo "4. 不影响子项目的独立Git仓库"
    echo

    if ! confirm_action "确定要清理历史跟踪记录吗？"; then
        log_info "操作已取消"
        return 1
    fi

    # 备份当前状态
    local backup_branch="backup-before-cleanup-$(date +%Y%m%d_%H%M%S)"
    git branch "$backup_branch" 2>/dev/null || true
    log_success "创建备份分支: $backup_branch"

    # 移除所有子项目文件的跟踪
    log_info "移除子项目文件跟踪..."
    for project in "${SUBPROJECTS[@]}"; do
        if git ls-files "$project/" | head -1 | grep -q .; then
            log_info "移除 $project/ 的跟踪记录"
            git rm -r --cached "$project/" 2>/dev/null || true
        fi
    done

    # 提交清理更改
    if ! git diff --cached --quiet; then
        git commit -m "Clean: Remove subproject tracking from history

This commit removes all tracking of subproject files from the main
repository history. Subprojects will be managed as independent
repositories while maintaining their content for backup purposes.

Cleaned projects: $(echo "${SUBPROJECTS[@]}" | tr ' ' ', ')
Cleanup date: $(date -Iseconds)"

        log_success "历史清理提交完成"
    else
        log_info "没有需要清理的跟踪记录"
    fi

    return 0
}

# 建立备份基线
establish_backup_baseline() {
    echo -e "${CYAN}=== 📋 建立备份基线 ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    # 添加主项目文件
    log_info "添加主项目文件..."
    git add README.md git.sh guide.md 2>/dev/null || true
    git add shell/ shared/ 2>/dev/null || true

    # 添加子项目内容（排除.git目录）
    for project in "${SUBPROJECTS[@]}"; do
        if [ -d "$project" ]; then
            log_info "添加子项目内容: $project"

            # 使用find排除.git目录
            find "$project" -type f -not -path "*/.git/*" -print0 | while IFS= read -r -d '' file; do
                git add "$file" 2>/dev/null || true
            done
        fi
    done

    # 检查添加的文件
    local added_files=$(git diff --cached --name-only | wc -l)
    log_info "准备提交 $added_files 个文件"

    if [ "$added_files" -eq 0 ]; then
        log_warning "没有文件需要提交"
        return 0
    fi

    # 提交基线
    local baseline_message="Establish backup baseline

This commit establishes a clean backup baseline containing:
- Main project files and configuration
- Current state of all subprojects (content only)
- Proper .gitignore configuration

Subprojects included: $(echo "${SUBPROJECTS[@]}" | tr ' ' ', ')
Baseline date: $(date -Iseconds)
Total files: $added_files

Note: Subproject .git directories are excluded to maintain
their independence while preserving complete backup capability."

    if git commit -m "$baseline_message"; then
        log_success "备份基线建立完成"
        return 0
    else
        log_error "备份基线建立失败"
        return 1
    fi
}

# 执行完整备份
perform_full_backup() {
    echo -e "${CYAN}=== 💾 执行完整备份 ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    # 分析当前状态
    analyze_project_status

    echo
    if ! confirm_action "开始执行完整备份？"; then
        log_info "备份已取消"
        return 1
    fi

    # 检查是否有旧的跟踪记录
    local has_old_tracking=false
    for project in "${SUBPROJECTS[@]}"; do
        if git ls-files "$project/" | head -1 | grep -q .; then
            has_old_tracking=true
            break
        fi
    done

    if [ "$has_old_tracking" = true ]; then
        echo
        log_warning "检测到旧的跟踪记录"
        if confirm_action "是否清理历史跟踪记录？"; then
            clean_history_tracking
        fi
    fi

    # 建立备份基线
    echo
    log_info "建立备份基线..."
    establish_backup_baseline

    # 推送到远程
    echo
    log_info "推送备份到远程仓库..."
    if git push origin $(git branch --show-current) 2>&1; then
        log_success "完整备份完成！"
        return 0
    else
        log_error "备份推送失败"
        return 1
    fi
}

# 增量备份更新
perform_incremental_backup() {
    echo -e "${CYAN}=== 🔄 执行增量备份 ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    # 检查是否有更改
    if git diff --quiet && git diff --cached --quiet; then
        # 检查未跟踪的文件
        local untracked_files=$(git ls-files --others --exclude-standard | wc -l)
        if [ "$untracked_files" -eq 0 ]; then
            log_info "没有检测到更改，跳过备份"
            return 0
        fi
    fi

    log_info "检测到项目更改，准备增量备份..."

    # 添加所有更改（排除.git目录）
    for project in "${SUBPROJECTS[@]}"; do
        if [ -d "$project" ]; then
            find "$project" -type f -not -path "*/.git/*" -print0 | while IFS= read -r -d '' file; do
                git add "$file" 2>/dev/null || true
            done
        fi
    done

    # 添加主项目文件
    git add README.md git.sh guide.md shell/ shared/ 2>/dev/null || true

    # 检查是否有更改需要提交
    if git diff --cached --quiet; then
        log_info "没有需要提交的更改"
        return 0
    fi

    # 生成提交消息
    local added_files=$(git diff --cached --name-only | wc -l)
    local commit_msg="Incremental backup update

Updated files: $added_files
Update time: $(date -Iseconds)

This is an automated incremental backup containing
the latest changes to the workspace and all subprojects."

    # 提交更改
    if git commit -m "$commit_msg"; then
        log_success "增量更改已提交"

        # 推送更改
        if git push origin $(git branch --show-current) 2>&1; then
            log_success "增量备份完成"
            return 0
        else
            log_error "增量备份推送失败"
            return 1
        fi
    else
        log_error "增量备份提交失败"
        return 1
    fi
}

# 验证备份完整性
verify_backup_integrity() {
    echo -e "${CYAN}=== ✅ 验证备份完整性 ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    local errors=0

    # 检查Git仓库完整性
    log_info "检查Git仓库完整性..."
    if git fsck --full 2>/dev/null; then
        log_success "Git仓库完整性检查通过"
    else
        log_error "Git仓库完整性检查失败"
        errors=$((errors + 1))
    fi

    # 检查远程同步状态
    log_info "检查远程同步状态..."
    local local_commit=$(git rev-parse HEAD)
    local remote_commit=$(git rev-parse origin/$(git branch --show-current) 2>/dev/null || echo "unknown")

    if [ "$local_commit" = "$remote_commit" ]; then
        log_success "本地与远程同步"
    else
        log_warning "本地与远程不同步"
        log_info "本地: $local_commit"
        log_info "远程: $remote_commit"
    fi

    # 检查子项目完整性
    log_info "检查子项目完整性..."
    for project in "${SUBPROJECTS[@]}"; do
        if [ -d "$project" ]; then
            local project_files=$(find "$project" -type f -not -path "*/.git/*" | wc -l)
            if [ "$project_files" -gt 0 ]; then
                log_success "$project: $project_files 个文件"
            else
                log_error "$project: 没有文件"
                errors=$((errors + 1))
            fi
        else
            log_error "$project: 目录不存在"
            errors=$((errors + 1))
        fi
    done

    # 总结
    echo
    if [ $errors -eq 0 ]; then
        log_success "备份完整性验证通过"
        return 0
    else
        log_error "发现 $errors 个完整性问题"
        return 1
    fi
}

# 显示使用指南
show_usage_guide() {
    echo -e "${CYAN}=== 📚 完整备份推送系统使用指南 ===${NC}"

    echo
    echo "========================================"
    echo "🎯 系统功能说明"
    echo "========================================"
    echo "本系统将混合项目作为完整备份推送到私有库，具有以下特点："
    echo "• 🔒 保持子项目完全独立：不影响子项目的Git仓库"
    echo "• 💾 完整内容备份：包含所有子项目的完整源代码"
    echo "• 🚀 大文件支持：自动处理大文件上传"
    echo "• 🧹 历史清理：清理旧的跟踪记录，建立干净基线"
    echo "• 📦 智能忽略：自动排除不必要的文件和目录"
    echo

    echo "========================================"
    echo "🚀 常用操作流程"
    echo "========================================"
    echo "1. 首次使用："
    echo "   选择 1 - 分析项目状态"
    echo "   选择 2 - 执行完整备份"
    echo
    echo "2. 日常更新："
    echo "   选择 3 - 增量备份更新"
    echo
    echo "3. 验证备份："
    echo "   选择 4 - 验证备份完整性"
    echo
    echo "4. 系统维护："
    echo "   选择 5 - 清理历史记录"
    echo

    echo "========================================"
    echo "⚠️ 重要说明"
    echo "========================================"
    echo "• 子项目的.git目录会被自动忽略，保持独立性"
    echo "• 大文件会被检测并提示处理方案"
    echo "• 系统会自动清理临时文件和日志"
    echo "• 备份不会影响子项目的正常开发流程"
    echo "• 默认按Enter键表示确认(Y)"
    echo

    wait_for_key
}