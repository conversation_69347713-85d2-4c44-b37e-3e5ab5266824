#!/bin/bash

# Git仓库自动恢复脚本
# 版本: v1.0
# 功能: 从Git Bundle恢复子项目的完整Git仓库

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 子项目列表
SUBPROJECTS=("gpt-load" "love" "new-api")
BUNDLE_DIR="git-bundles"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 确认操作
confirm_action() {
    local message="$1"
    echo -n -e "${YELLOW}$message [Y/n]: ${NC}"
    read -r response
    case "$response" in
        ""|[Yy]|[Yy][Ee][Ss]) return 0 ;;
        *) return 1 ;;
    esac
}

# 恢复单个项目的Git仓库
restore_project_git() {
    local project="$1"
    local bundle_file="$BUNDLE_DIR/${project}.bundle"

    if [ ! -f "$bundle_file" ]; then
        log_warning "$project: Bundle文件不存在，跳过"
        return 1
    fi

    if [ ! -d "$project" ]; then
        log_error "$project: 项目目录不存在"
        return 1
    fi

    if [ -d "$project/.git" ]; then
        log_warning "$project: 已存在Git仓库"
        if ! confirm_action "是否覆盖现有Git仓库？"; then
            return 1
        fi
        rm -rf "$project/.git"
    fi

    log_info "恢复 $project 的Git仓库..."

    # 创建临时目录
    local temp_dir=$(mktemp -d)

    # 从bundle克隆到临时目录
    if git clone "$bundle_file" "$temp_dir" 2>/dev/null; then
        # 移动.git目录到项目中
        mv "$temp_dir/.git" "$project/"

        # 进入项目目录重置工作区
        cd "$project"
        git reset --hard HEAD >/dev/null 2>&1

        # 显示恢复后的状态
        local branch=$(git branch --show-current 2>/dev/null || echo "detached")
        local commit=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

        log_success "$project Git仓库恢复完成 (分支: $branch, 提交: $commit)"
        cd ..

        # 清理临时目录
        rm -rf "$temp_dir"
        return 0
    else
        log_error "$project Git仓库恢复失败"
        rm -rf "$temp_dir"
        return 1
    fi
}

# 主函数
main() {
    echo -e "${CYAN}=== 🔄 Git仓库恢复工具 ===${NC}"
    echo

    if [ ! -d "$BUNDLE_DIR" ]; then
        log_error "Bundle目录不存在: $BUNDLE_DIR"
        echo "请确保在包含Git Bundle的目录中运行此脚本"
        exit 1
    fi

    # 统计可恢复的项目
    local available_bundles=0
    for project in "${SUBPROJECTS[@]}"; do
        if [ -f "$BUNDLE_DIR/${project}.bundle" ]; then
            available_bundles=$((available_bundles + 1))
        fi
    done

    if [ $available_bundles -eq 0 ]; then
        log_error "没有找到任何Bundle文件"
        exit 1
    fi

    log_info "找到 $available_bundles 个Bundle文件"
    echo

    if ! confirm_action "开始恢复Git仓库？"; then
        log_info "操作已取消"
        exit 0
    fi

    echo
    local success_count=0

    # 恢复每个项目
    for project in "${SUBPROJECTS[@]}"; do
        if restore_project_git "$project"; then
            success_count=$((success_count + 1))
        fi
    done

    echo
    if [ $success_count -gt 0 ]; then
        log_success "🎉 成功恢复 $success_count 个项目的Git仓库！"
        echo
        echo "现在您可以在各个子项目中正常使用Git命令："
        echo "  cd <project-name>"
        echo "  git status"
        echo "  git log"
        echo "  git push"
    else
        log_error "没有成功恢复任何Git仓库"
        exit 1
    fi
}

# 如果直接执行则调用主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
